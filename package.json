{"name": "be_management", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "8", "mongoose": "^8.15.2", "remove-accents": "^0.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tsoa": "^6.6.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "8", "@types/node": "^24.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}