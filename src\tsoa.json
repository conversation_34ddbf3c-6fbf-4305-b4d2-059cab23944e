{"entryFile": "src/server.ts", "noImplicitAdditionalProperties": "throw-on-extras", "controllerPathGlobs": ["src/controllers/*.ts"], "spec": {"outputDirectory": "src/swagger", "specVersion": 3, "basePath": "/api", "securityDefinitions": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}, "routes": {"routesDir": "src/routes", "authenticationModule": "src/middlewares/authentication.ts"}}