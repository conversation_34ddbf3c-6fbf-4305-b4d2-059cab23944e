import {
  Body,
  Controller,
  Get,
  Path,
  Post,
  Put,
  Route,
  Security,
  Tags,
} from "tsoa";
import { generateToken } from "../utils/generateToken";

interface UserResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  token: string;
}

interface LoginRequest {
  email: string;
  password: string;
}

@Route("auth")
@Tags("Auth")
export class UserController extends Controller {
  /**
   * Login with email and password
   */
  @Post("login")
  public async login(@Body() requestBody: LoginRequest): Promise<UserResponse> {
    // Gi<PERSON> lập logic đăng nhập
    const userId = "user123";
    const token = generateToken(userId);

    return {
      id: userId,
      name: "Test User",
      email: requestBody.email,
      role: "user",
      token,
    };
  }

  /**
   * Get current user profile
   */
  @Get("me")
  @Security("bearerAuth")
  public async getMe(): Promise<UserResponse> {
    // <PERSON><PERSON><PERSON> lập logic lấy thông tin user
    return {
      id: "user123",
      name: "Test User",
      email: "<EMAIL>",
      role: "user",
      token: "",
    };
  }

  /**
   * Update user profile
   */
  @Put("update/{userId}")
  @Security("bearerAuth")
  public async updateUser(
    @Path() userId: string,
    @Body() requestBody: { name?: string; email?: string }
  ): Promise<UserResponse> {
    // Giả lập logic cập nhật user
    return {
      id: userId,
      name: requestBody.name || "Updated User",
      email: requestBody.email || "<EMAIL>",
      role: "user",
      token: "",
    };
  }
}
